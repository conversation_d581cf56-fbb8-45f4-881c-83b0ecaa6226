<template>
  <view class="composition-demo">
    <view class="demo-header">
      <text class="demo-title">Composition API 功能演示</text>
    </view>
    
    <!-- 计数器演示 -->
    <view class="demo-section">
      <view class="section-title">
        <text>计数器演示</text>
      </view>
      <view class="counter-display">
        <text class="counter-value">{{ count }}</text>
      </view>
      <view class="counter-controls">
        <button class="control-btn decrease" @click="decrement">-</button>
        <button class="control-btn reset" @click="reset">重置</button>
        <button class="control-btn increase" @click="increment">+</button>
      </view>
      <view class="counter-actions">
        <button class="action-btn" @click="incrementBy(5)">+5</button>
        <button class="action-btn" @click="incrementBy(10)">+10</button>
        <button class="action-btn" @click="setValue(100)">设为100</button>
      </view>
    </view>
    
    <!-- 用户信息演示 -->
    <view class="demo-section">
      <view class="section-title">
        <text>API请求演示</text>
      </view>
      <view class="user-info" v-if="userInfo.name">
        <view class="user-item">
          <text class="label">姓名:</text>
          <text class="value">{{ userInfo.name }}</text>
        </view>
        <view class="user-item">
          <text class="label">邮箱:</text>
          <text class="value">{{ userInfo.email }}</text>
        </view>
        <view class="user-item">
          <text class="label">ID:</text>
          <text class="value">{{ userInfo.id }}</text>
        </view>
      </view>
      <view class="user-actions">
        <button 
          class="api-btn" 
          @click="fetchUser"
          :disabled="loading"
        >
          {{ loading ? '加载中...' : '获取用户信息' }}
        </button>
        <button class="api-btn secondary" @click="clearUser">清空信息</button>
      </view>
      <view class="error-message" v-if="error">
        <text>{{ error }}</text>
      </view>
    </view>
    
    <!-- 本地存储演示 -->
    <view class="demo-section">
      <view class="section-title">
        <text>本地存储演示</text>
      </view>
      <view class="storage-content">
        <view class="preference-item">
          <text class="pref-label">主题:</text>
          <picker 
            :value="themeIndex" 
            :range="themes" 
            @change="onThemeChange"
          >
            <view class="picker-value">{{ preferences.theme }}</view>
          </picker>
        </view>
        <view class="preference-item">
          <text class="pref-label">通知:</text>
          <switch 
            :checked="preferences.notifications" 
            @change="toggleNotifications"
          />
        </view>
        <view class="preference-item">
          <text class="pref-label">自动保存:</text>
          <switch 
            :checked="preferences.autoSave" 
            @change="toggleAutoSave"
          />
        </view>
      </view>
      <view class="storage-actions">
        <button class="storage-btn" @click="savePreferences">保存设置</button>
        <button class="storage-btn secondary" @click="clearPreferences">清空设置</button>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useCounter } from '../composables/useCounter.js'
import { useApi } from '../composables/useApi.js'
import { useUserPreferences } from '../composables/useStorage.js'

export default {
  name: 'CompositionDemo',
  setup() {
    // 使用计数器组合式函数
    const { 
      count, 
      increment, 
      decrement, 
      reset, 
      setValue, 
      incrementBy 
    } = useCounter(0)
    
    // 使用API组合式函数
    const { 
      loading, 
      error, 
      userInfo, 
      fetchUser, 
      clearUser 
    } = useApi()
    
    // 使用用户偏好设置组合式函数
    const {
      preferences,
      setTheme,
      toggleNotifications,
      toggleAutoSave,
      save: savePreferences,
      clear: clearPreferences
    } = useUserPreferences()
    
    // 主题选项
    const themes = ['light', 'dark', 'auto']
    const themeIndex = computed(() => {
      return themes.indexOf(preferences.value.theme)
    })
    
    // 主题变更处理
    const onThemeChange = (e) => {
      const selectedTheme = themes[e.detail.value]
      setTheme(selectedTheme)
    }
    
    // 生命周期
    onMounted(() => {
      console.log('CompositionDemo 组件已挂载')
      console.log('当前偏好设置:', preferences.value)
    })
    
    return {
      // 计数器相关
      count,
      increment,
      decrement,
      reset,
      setValue,
      incrementBy,
      
      // API相关
      loading,
      error,
      userInfo,
      fetchUser,
      clearUser,
      
      // 存储相关
      preferences,
      themes,
      themeIndex,
      onThemeChange,
      toggleNotifications,
      toggleAutoSave,
      savePreferences,
      clearPreferences
    }
  }
}
</script>

<style scoped>
.composition-demo {
  padding: 20rpx;
}

.demo-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.demo-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.demo-section {
  background: #fff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

/* 计数器样式 */
.counter-display {
  text-align: center;
  margin-bottom: 30rpx;
}

.counter-value {
  font-size: 60rpx;
  font-weight: bold;
  color: #007aff;
}

.counter-controls {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.control-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.control-btn.decrease {
  background: #ff3b30;
}

.control-btn.reset {
  background: #ff9500;
}

.control-btn.increase {
  background: #34c759;
}

.counter-actions {
  display: flex;
  justify-content: center;
  gap: 15rpx;
}

.action-btn {
  padding: 15rpx 25rpx;
  border-radius: 20rpx;
  border: none;
  background: #007aff;
  color: white;
  font-size: 24rpx;
}

/* 用户信息样式 */
.user-info {
  margin-bottom: 30rpx;
}

.user-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.label {
  font-weight: bold;
  color: #666;
}

.value {
  color: #333;
}

.user-actions {
  display: flex;
  gap: 20rpx;
}

.api-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  border: none;
  background: #007aff;
  color: white;
  font-size: 28rpx;
}

.api-btn.secondary {
  background: #ff9500;
}

.api-btn:disabled {
  background: #ccc;
}

.error-message {
  margin-top: 20rpx;
  padding: 15rpx;
  background: #ffebee;
  border-radius: 8rpx;
  color: #d32f2f;
  font-size: 24rpx;
}

/* 存储设置样式 */
.preference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.pref-label {
  font-size: 28rpx;
  color: #333;
}

.picker-value {
  color: #007aff;
  font-size: 28rpx;
}

.storage-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.storage-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  border: none;
  background: #34c759;
  color: white;
  font-size: 28rpx;
}

.storage-btn.secondary {
  background: #ff3b30;
}
</style>
