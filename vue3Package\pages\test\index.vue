<template>
  <view class="vue3-test-page">
    <!-- 页面导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <view class="navbar-center">
        <text class="navbar-title">Vue3 测试页面</text>
      </view>
      <view class="navbar-right" @click="showMenu">
        <text class="menu-icon">⋯</text>
      </view>
    </view>
    
    <!-- 测试功能区域 -->
    <view class="test-sections">
      
      <!-- 响应式数据测试 -->
      <view class="test-section">
        <view class="section-header">
          <text class="section-title">响应式数据测试</text>
          <text class="section-desc">测试 ref 和 reactive 的响应式特性</text>
        </view>
        
        <view class="test-content">
          <view class="data-display">
            <view class="data-item">
              <text class="data-label">字符串 (ref):</text>
              <text class="data-value">{{ testString }}</text>
            </view>
            <view class="data-item">
              <text class="data-label">数字 (ref):</text>
              <text class="data-value">{{ testNumber }}</text>
            </view>
            <view class="data-item">
              <text class="data-label">对象 (reactive):</text>
              <text class="data-value">{{ JSON.stringify(testObject) }}</text>
            </view>
          </view>
          
          <view class="test-controls">
            <button class="test-btn" @click="updateString">更新字符串</button>
            <button class="test-btn" @click="incrementNumber">增加数字</button>
            <button class="test-btn" @click="updateObject">更新对象</button>
          </view>
        </view>
      </view>
      
      <!-- 计算属性测试 -->
      <view class="test-section">
        <view class="section-header">
          <text class="section-title">计算属性测试</text>
          <text class="section-desc">测试 computed 的自动更新特性</text>
        </view>
        
        <view class="test-content">
          <view class="computed-display">
            <view class="computed-item">
              <text class="computed-label">原始值:</text>
              <text class="computed-value">{{ baseValue }}</text>
            </view>
            <view class="computed-item">
              <text class="computed-label">双倍值:</text>
              <text class="computed-value">{{ doubleValue }}</text>
            </view>
            <view class="computed-item">
              <text class="computed-label">格式化文本:</text>
              <text class="computed-value">{{ formattedText }}</text>
            </view>
          </view>
          
          <view class="computed-controls">
            <input 
              class="value-input"
              v-model.number="baseValue"
              type="number"
              placeholder="输入数字"
            />
            <button class="test-btn" @click="randomizeValue">随机值</button>
          </view>
        </view>
      </view>
      
      <!-- 监听器测试 -->
      <view class="test-section">
        <view class="section-header">
          <text class="section-title">监听器测试</text>
          <text class="section-desc">测试 watch 和 watchEffect 的监听功能</text>
        </view>
        
        <view class="test-content">
          <view class="watch-display">
            <view class="watch-item">
              <text class="watch-label">监听的值:</text>
              <text class="watch-value">{{ watchedValue }}</text>
            </view>
            <view class="watch-item">
              <text class="watch-label">变化次数:</text>
              <text class="watch-value">{{ changeCount }}</text>
            </view>
            <view class="watch-item">
              <text class="watch-label">最后变化时间:</text>
              <text class="watch-value">{{ lastChangeTime }}</text>
            </view>
          </view>
          
          <view class="watch-controls">
            <input 
              class="watch-input"
              v-model="watchedValue"
              placeholder="输入内容观察变化"
            />
            <button class="test-btn" @click="clearWatchedValue">清空</button>
          </view>
          
          <view class="watch-log" v-if="watchLog.length > 0">
            <text class="log-title">变化日志:</text>
            <view 
              class="log-item" 
              v-for="(log, index) in watchLog" 
              :key="index"
            >
              <text class="log-text">{{ log }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 生命周期测试 */
      <view class="test-section">
        <view class="section-header">
          <text class="section-title">生命周期测试</text>
          <text class="section-desc">测试 Vue3 生命周期钩子</text>
        </view>
        
        <view class="test-content">
          <view class="lifecycle-display">
            <view 
              class="lifecycle-item"
              v-for="(hook, index) in lifecycleHooks"
              :key="index"
            >
              <text class="lifecycle-name">{{ hook.name }}:</text>
              <text class="lifecycle-time">{{ hook.time }}</text>
            </view>
          </view>
          
          <view class="lifecycle-controls">
            <button class="test-btn" @click="triggerUpdate">触发更新</button>
            <button class="test-btn" @click="clearLifecycleLog">清空日志</button>
          </view>
        </view>
      </view>
      
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <button class="action-btn primary" @click="runAllTests">运行所有测试</button>
      <button class="action-btn secondary" @click="resetAllData">重置所有数据</button>
      <button class="action-btn success" @click="exportTestResults">导出结果</button>
    </view>
    
    <!-- 测试结果弹窗 -->
    <view class="test-modal" v-if="showTestModal" @click="closeTestModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">测试结果</text>
          <text class="modal-close" @click="closeTestModal">×</text>
        </view>
        <view class="modal-body">
          <text class="result-text">{{ testResults }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { 
  ref, 
  reactive, 
  computed, 
  watch, 
  watchEffect,
  onMounted, 
  onUpdated,
  onBeforeUnmount,
  nextTick
} from 'vue'

export default {
  name: 'Vue3TestPage',
  setup() {
    // 响应式数据
    const testString = ref('初始字符串')
    const testNumber = ref(0)
    const testObject = reactive({
      name: '测试对象',
      count: 1,
      active: true
    })
    
    // 计算属性测试
    const baseValue = ref(10)
    const doubleValue = computed(() => baseValue.value * 2)
    const formattedText = computed(() => {
      return `基础值: ${baseValue.value}, 双倍值: ${doubleValue.value}`
    })
    
    // 监听器测试
    const watchedValue = ref('')
    const changeCount = ref(0)
    const lastChangeTime = ref('')
    const watchLog = reactive([])
    
    // 生命周期记录
    const lifecycleHooks = reactive([])
    
    // 测试结果
    const showTestModal = ref(false)
    const testResults = ref('')
    
    // 页面参数
    const pageParams = ref({})
    
    // 方法定义
    const updateString = () => {
      const randomStrings = ['Hello Vue3!', '响应式更新', '测试成功', 'Composition API']
      const randomIndex = Math.floor(Math.random() * randomStrings.length)
      testString.value = randomStrings[randomIndex]
    }
    
    const incrementNumber = () => {
      testNumber.value += Math.floor(Math.random() * 10) + 1
    }
    
    const updateObject = () => {
      testObject.count += 1
      testObject.name = `更新对象 ${testObject.count}`
      testObject.active = !testObject.active
    }
    
    const randomizeValue = () => {
      baseValue.value = Math.floor(Math.random() * 100) + 1
    }
    
    const clearWatchedValue = () => {
      watchedValue.value = ''
    }
    
    const triggerUpdate = () => {
      // 触发组件更新
      testString.value = `更新时间: ${new Date().toLocaleTimeString()}`
    }
    
    const clearLifecycleLog = () => {
      lifecycleHooks.splice(0, lifecycleHooks.length)
    }
    
    const addLifecycleHook = (name) => {
      lifecycleHooks.push({
        name,
        time: new Date().toLocaleTimeString()
      })
    }
    
    const runAllTests = () => {
      updateString()
      incrementNumber()
      updateObject()
      randomizeValue()
      
      testResults.value = `
测试完成时间: ${new Date().toLocaleString()}
字符串值: ${testString.value}
数字值: ${testNumber.value}
对象状态: ${JSON.stringify(testObject)}
计算属性: ${formattedText.value}
监听变化次数: ${changeCount.value}
      `.trim()
      
      showTestModal.value = true
    }
    
    const resetAllData = () => {
      testString.value = '初始字符串'
      testNumber.value = 0
      Object.assign(testObject, {
        name: '测试对象',
        count: 1,
        active: true
      })
      baseValue.value = 10
      watchedValue.value = ''
      changeCount.value = 0
      lastChangeTime.value = ''
      watchLog.splice(0, watchLog.length)
      clearLifecycleLog()
    }
    
    const exportTestResults = () => {
      const results = {
        timestamp: new Date().toISOString(),
        testData: {
          string: testString.value,
          number: testNumber.value,
          object: testObject,
          computed: formattedText.value,
          watchCount: changeCount.value
        },
        lifecycle: lifecycleHooks
      }
      
      console.log('测试结果导出:', results)
      
      uni.showToast({
        title: '结果已导出到控制台',
        icon: 'success'
      })
    }
    
    const closeTestModal = () => {
      showTestModal.value = false
    }
    
    const goBack = () => {
      uni.navigateBack()
    }
    
    const showMenu = () => {
      uni.showActionSheet({
        itemList: ['重置数据', '运行测试', '导出结果'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              resetAllData()
              break
            case 1:
              runAllTests()
              break
            case 2:
              exportTestResults()
              break
          }
        }
      })
    }
    
    // 监听器设置
    watch(watchedValue, (newValue, oldValue) => {
      changeCount.value++
      lastChangeTime.value = new Date().toLocaleTimeString()
      watchLog.push(`${lastChangeTime.value}: "${oldValue}" → "${newValue}"`)
      
      // 限制日志数量
      if (watchLog.length > 5) {
        watchLog.shift()
      }
    })
    
    // watchEffect 示例
    watchEffect(() => {
      if (baseValue.value > 50) {
        console.log('基础值超过50:', baseValue.value)
      }
    })
    
    // 生命周期钩子
    onMounted(() => {
      addLifecycleHook('onMounted')
      console.log('Vue3测试页面已挂载')
      
      // 获取页面参数
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      pageParams.value = currentPage.options || {}
      
      if (pageParams.value.from) {
        console.log('来源页面:', pageParams.value.from)
      }
    })
    
    onUpdated(() => {
      addLifecycleHook('onUpdated')
      console.log('Vue3测试页面已更新')
    })
    
    onBeforeUnmount(() => {
      addLifecycleHook('onBeforeUnmount')
      console.log('Vue3测试页面即将卸载')
    })
    
    return {
      // 响应式数据
      testString,
      testNumber,
      testObject,
      
      // 计算属性
      baseValue,
      doubleValue,
      formattedText,
      
      // 监听器相关
      watchedValue,
      changeCount,
      lastChangeTime,
      watchLog,
      
      // 生命周期
      lifecycleHooks,
      
      // 测试结果
      showTestModal,
      testResults,
      
      // 方法
      updateString,
      incrementNumber,
      updateObject,
      randomizeValue,
      clearWatchedValue,
      triggerUpdate,
      clearLifecycleLog,
      runAllTests,
      resetAllData,
      exportTestResults,
      closeTestModal,
      goBack,
      showMenu
    }
  }
}
</script>

<style scoped>
.vue3-test-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #e0e0e0;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.back-icon {
  font-size: 32rpx;
  color: #007aff;
}

.back-text {
  font-size: 28rpx;
  color: #007aff;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.menu-icon {
  font-size: 32rpx;
  color: #666;
}

.test-sections {
  padding: 20rpx;
}

.test-section {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.section-desc {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

.test-content {
  padding: 30rpx;
}

.data-display, .computed-display, .watch-display, .lifecycle-display {
  margin-bottom: 30rpx;
}

.data-item, .computed-item, .watch-item, .lifecycle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.data-item:last-child, .computed-item:last-child, 
.watch-item:last-child, .lifecycle-item:last-child {
  border-bottom: none;
}

.data-label, .computed-label, .watch-label, .lifecycle-name {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.data-value, .computed-value, .watch-value, .lifecycle-time {
  font-size: 28rpx;
  color: #333;
  max-width: 60%;
  text-align: right;
  word-break: break-all;
}

.test-controls, .computed-controls, .watch-controls, .lifecycle-controls {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.test-btn {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.value-input, .watch-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.watch-log {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.log-title {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.log-item {
  padding: 10rpx 15rpx;
  background: #f8f9fa;
  border-radius: 6rpx;
  margin-bottom: 8rpx;
}

.log-text {
  font-size: 24rpx;
  color: #333;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 1rpx;
  background: white;
  padding: 20rpx;
  border-top: 1rpx solid #e0e0e0;
}

.action-btn {
  flex: 1;
  padding: 25rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  color: white;
}

.action-btn.primary {
  background: #007aff;
}

.action-btn.secondary {
  background: #ff9500;
}

.action-btn.success {
  background: #34c759;
}

.test-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 15rpx;
  width: 80%;
  max-height: 70%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #666;
}

.modal-body {
  padding: 30rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.result-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-line;
}
</style>
