<template>
  <view class="vue3-test-page">
    <!-- 页面导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
      <view class="navbar-center">
        <text class="navbar-title">Vue3 测试页面</text>
      </view>
      <view class="navbar-right" @click="showMenu">
        <text class="menu-icon">⋯</text>
      </view>
    </view>
    
    <!-- 测试功能区域 -->
    <view class="test-sections">
      
      <!-- 响应式数据测试 -->
      <view class="test-section">
        <view class="section-header">
          <text class="section-title">响应式数据测试</text>
          <text class="section-desc">测试 ref 和 reactive 的响应式特性</text>
        </view>
        
        <view class="test-content">
          <view class="data-display">
            <view class="data-item">
              <text class="data-label">字符串 (ref):</text>
              <text class="data-value">{{ testString }}</text>
            </view>
            <view class="data-item">
              <text class="data-label">数字 (ref):</text>
              <text class="data-value">{{ testNumber }}</text>
            </view>
            <view class="data-item">
              <text class="data-label">对象 (reactive):</text>
              <text class="data-value">{{ JSON.stringify(testObject) }}</text>
            </view>
          </view>
          
          <view class="test-controls">
            <button class="test-btn" @click="updateString">更新字符串</button>
            <button class="test-btn" @click="incrementNumber">增加数字</button>
            <button class="test-btn" @click="updateObject">更新对象</button>
          </view>
        </view>
      </view>
      
      <!-- 计算属性测试 -->
      <view class="test-section">
        <view class="section-header">
          <text class="section-title">计算属性测试</text>
          <text class="section-desc">测试 computed 的自动更新特性</text>
        </view>
        
        <view class="test-content">
          <view class="computed-display">
            <view class="computed-item">
              <text class="computed-label">原始值:</text>
              <text class="computed-value">{{ baseValue }}</text>
            </view>
            <view class="computed-item">
              <text class="computed-label">双倍值:</text>
              <text class="computed-value">{{ doubleValue }}</text>
            </view>
          </view>
          
          <view class="computed-controls">
            <input 
              class="value-input"
              v-model.number="baseValue"
              type="number"
              placeholder="输入数字"
            />
            <button class="test-btn" @click="randomizeValue">随机值</button>
          </view>
        </view>
      </view>
      
      <!-- 底部操作栏 -->
      <view class="bottom-actions">
        <button class="action-btn primary" @click="runAllTests">运行所有测试</button>
        <button class="action-btn secondary" @click="resetAllData">重置所有数据</button>
      </view>
      
    </view>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'

export default {
  name: 'Vue3TestPage',
  setup() {
    // 响应式数据
    const testString = ref('初始字符串')
    const testNumber = ref(0)
    const testObject = reactive({
      name: '测试对象',
      count: 1,
      active: true
    })
    
    // 计算属性测试
    const baseValue = ref(10)
    const doubleValue = computed(() => baseValue.value * 2)
    
    // 方法定义
    const updateString = () => {
      const randomStrings = ['Hello Vue3!', '响应式更新', '测试成功', 'Composition API']
      const randomIndex = Math.floor(Math.random() * randomStrings.length)
      testString.value = randomStrings[randomIndex]
    }
    
    const incrementNumber = () => {
      testNumber.value += Math.floor(Math.random() * 10) + 1
    }
    
    const updateObject = () => {
      testObject.count += 1
      testObject.name = `更新对象 ${testObject.count}`
      testObject.active = !testObject.active
    }
    
    const randomizeValue = () => {
      baseValue.value = Math.floor(Math.random() * 100) + 1
    }
    
    const runAllTests = () => {
      updateString()
      incrementNumber()
      updateObject()
      randomizeValue()
      
      uni.showToast({
        title: '所有测试已运行',
        icon: 'success'
      })
    }
    
    const resetAllData = () => {
      testString.value = '初始字符串'
      testNumber.value = 0
      Object.assign(testObject, {
        name: '测试对象',
        count: 1,
        active: true
      })
      baseValue.value = 10
      
      uni.showToast({
        title: '数据已重置',
        icon: 'success'
      })
    }
    
    const goBack = () => {
      uni.navigateBack()
    }
    
    const showMenu = () => {
      uni.showActionSheet({
        itemList: ['重置数据', '运行测试'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              resetAllData()
              break
            case 1:
              runAllTests()
              break
          }
        }
      })
    }
    
    // 生命周期钩子
    onMounted(() => {
      console.log('Vue3测试页面已挂载')
    })
    
    return {
      // 响应式数据
      testString,
      testNumber,
      testObject,
      
      // 计算属性
      baseValue,
      doubleValue,
      
      // 方法
      updateString,
      incrementNumber,
      updateObject,
      randomizeValue,
      runAllTests,
      resetAllData,
      goBack,
      showMenu
    }
  }
}
</script>

<style scoped>
.vue3-test-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.custom-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #e0e0e0;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.back-icon {
  font-size: 32rpx;
  color: #007aff;
}

.back-text {
  font-size: 28rpx;
  color: #007aff;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.menu-icon {
  font-size: 32rpx;
  color: #666;
}

.test-sections {
  padding: 20rpx;
}

.test-section {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.section-desc {
  font-size: 24rpx;
  opacity: 0.9;
  display: block;
}

.test-content {
  padding: 30rpx;
}

.data-display, .computed-display {
  margin-bottom: 30rpx;
}

.data-item, .computed-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.data-item:last-child, .computed-item:last-child {
  border-bottom: none;
}

.data-label, .computed-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.data-value, .computed-value {
  font-size: 28rpx;
  color: #333;
  max-width: 60%;
  text-align: right;
  word-break: break-all;
}

.test-controls, .computed-controls {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.test-btn {
  flex: 1;
  min-width: 200rpx;
  padding: 20rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.value-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.bottom-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
  background: white;
  border-top: 1rpx solid #e0e0e0;
}

.action-btn {
  flex: 1;
  padding: 25rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  color: white;
  border-radius: 10rpx;
}

.action-btn.primary {
  background: #007aff;
}

.action-btn.secondary {
  background: #ff9500;
}
</style>
