import { ref } from 'vue'

/**
 * 计数器组合式函数
 * 演示Vue3 Composition API的基本用法
 */
export function useCounter(initialValue = 0) {
  // 响应式数据
  const count = ref(initialValue)
  
  // 增加计数
  const increment = () => {
    count.value++
  }
  
  // 减少计数
  const decrement = () => {
    count.value--
  }
  
  // 重置计数
  const reset = () => {
    count.value = initialValue
  }
  
  // 设置特定值
  const setValue = (value) => {
    count.value = value
  }
  
  // 批量增加
  const incrementBy = (amount) => {
    count.value += amount
  }
  
  return {
    count,
    increment,
    decrement,
    reset,
    setValue,
    incrementBy
  }
}
