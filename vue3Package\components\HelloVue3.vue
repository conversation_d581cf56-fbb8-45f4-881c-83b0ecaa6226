<template>
  <view class="hello-vue3">
    <view class="header">
      <text class="title">{{ title }}</text>
      <text class="subtitle">{{ subtitle }}</text>
    </view>
    
    <view class="content">
      <view class="message-box">
        <text class="message">{{ displayMessage }}</text>
      </view>
      
      <view class="input-section">
        <input 
          class="input"
          v-model="inputValue"
          placeholder="请输入新消息"
          @confirm="updateMessage"
        />
        <button class="btn-primary" @click="updateMessage">更新消息</button>
      </view>
      
      <view class="action-buttons">
        <button class="btn-secondary" @click="resetMessage">重置</button>
        <button class="btn-success" @click="randomMessage">随机消息</button>
      </view>
    </view>
    
    <view class="stats">
      <text class="stats-text">消息更新次数: {{ updateCount }}</text>
      <text class="stats-text">组件创建时间: {{ createdTime }}</text>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'

export default {
  name: 'HelloVue3',
  props: {
    message: {
      type: String,
      default: 'Hello Vue3!'
    },
    title: {
      type: String,
      default: 'Vue3 组件演示'
    }
  },
  emits: ['update', 'messageChange'],
  setup(props, { emit }) {
    // 响应式数据
    const inputValue = ref('')
    const updateCount = ref(0)
    const createdTime = ref('')
    const subtitle = ref('使用 Composition API 构建')
    
    // 计算属性
    const displayMessage = computed(() => {
      return props.message || '暂无消息'
    })
    
    // 随机消息数组
    const randomMessages = [
      'Vue3 真棒！',
      'Composition API 很强大',
      '响应式系统升级了',
      '性能更好了',
      '开发体验提升了'
    ]
    
    // 方法
    const updateMessage = () => {
      if (inputValue.value.trim()) {
        emit('update', inputValue.value)
        emit('messageChange', {
          oldMessage: props.message,
          newMessage: inputValue.value,
          timestamp: new Date().toLocaleString()
        })
        updateCount.value++
        inputValue.value = ''
      } else {
        uni.showToast({
          title: '请输入消息内容',
          icon: 'none'
        })
      }
    }
    
    const resetMessage = () => {
      emit('update', 'Hello Vue3!')
      updateCount.value++
    }
    
    const randomMessage = () => {
      const randomIndex = Math.floor(Math.random() * randomMessages.length)
      const message = randomMessages[randomIndex]
      emit('update', message)
      updateCount.value++
    }
    
    // 监听props变化
    watch(
      () => props.message,
      (newMessage, oldMessage) => {
        console.log('消息变化:', { oldMessage, newMessage })
      }
    )
    
    // 生命周期
    onMounted(() => {
      createdTime.value = new Date().toLocaleString()
      console.log('HelloVue3 组件已挂载')
    })
    
    return {
      inputValue,
      updateCount,
      createdTime,
      subtitle,
      displayMessage,
      updateMessage,
      resetMessage,
      randomMessage
    }
  }
}
</script>

<style scoped>
.hello-vue3 {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  margin: 20rpx;
  color: white;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 24rpx;
  opacity: 0.8;
  display: block;
}

.content {
  margin-bottom: 30rpx;
}

.message-box {
  background: rgba(255, 255, 255, 0.2);
  padding: 30rpx;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.message {
  font-size: 32rpx;
  font-weight: 500;
}

.input-section {
  margin-bottom: 30rpx;
}

.input {
  width: 100%;
  padding: 20rpx;
  border-radius: 10rpx;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.btn-primary, .btn-secondary, .btn-success {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  border: none;
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}

.btn-primary {
  background: #007aff;
}

.btn-secondary {
  background: #ff9500;
}

.btn-success {
  background: #34c759;
}

.stats {
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  padding-top: 20rpx;
  text-align: center;
}

.stats-text {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
}
</style>
