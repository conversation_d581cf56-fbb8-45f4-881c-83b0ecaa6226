<template>
  <view class="vue2-integration-example">
    <view class="header">
      <text class="title">Vue2 集成 Vue3 分包示例</text>
      <text class="subtitle">演示如何在Vue2页面中使用Vue3功能</text>
    </view>
    
    <view class="integration-sections">
      
      <!-- 页面跳转演示 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">页面跳转演示</text>
        </view>
        <view class="section-content">
          <button class="demo-btn primary" @click="goToVue3Demo">
            跳转到Vue3演示页面
          </button>
          <button class="demo-btn secondary" @click="goToVue3Test">
            跳转到Vue3测试页面
          </button>
          <button class="demo-btn success" @click="goToVue3DemoWithParams">
            带参数跳转到Vue3页面
          </button>
        </view>
      </view>
      
      <!-- 数据传递演示 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">数据传递演示</text>
        </view>
        <view class="section-content">
          <view class="input-group">
            <text class="input-label">用户ID:</text>
            <input 
              class="input-field"
              v-model="userId"
              placeholder="输入用户ID"
              type="number"
            />
          </view>
          <view class="input-group">
            <text class="input-label">消息内容:</text>
            <input 
              class="input-field"
              v-model="message"
              placeholder="输入要传递的消息"
            />
          </view>
          <button class="demo-btn info" @click="passDataToVue3">
            传递数据到Vue3页面
          </button>
        </view>
      </view>
      
      <!-- 全局数据演示 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">全局数据演示</text>
        </view>
        <view class="section-content">
          <view class="data-display">
            <text class="data-label">当前全局数据:</text>
            <text class="data-value">{{ JSON.stringify(globalData) }}</text>
          </view>
          <view class="data-controls">
            <button class="control-btn" @click="setGlobalData">设置全局数据</button>
            <button class="control-btn" @click="clearGlobalData">清空全局数据</button>
            <button class="control-btn" @click="refreshGlobalData">刷新显示</button>
          </view>
        </view>
      </view>
      
      <!-- 本地存储演示 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">本地存储演示</text>
        </view>
        <view class="section-content">
          <view class="storage-display">
            <text class="storage-label">存储的数据:</text>
            <text class="storage-value">{{ storedData || '暂无数据' }}</text>
          </view>
          <view class="storage-controls">
            <input 
              class="storage-input"
              v-model="storageInput"
              placeholder="输入要存储的数据"
            />
            <button class="control-btn" @click="saveToStorage">保存数据</button>
            <button class="control-btn" @click="loadFromStorage">读取数据</button>
            <button class="control-btn" @click="clearStorage">清空存储</button>
          </view>
        </view>
      </view>
      
      <!-- Vue3分包信息 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">Vue3分包信息</text>
        </view>
        <view class="section-content">
          <view class="info-item">
            <text class="info-label">分包状态:</text>
            <text class="info-value" :class="{ 'status-active': vue3Available }">
              {{ vue3Available ? '可用' : '不可用' }}
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">分包版本:</text>
            <text class="info-value">{{ vue3Info.version || '未知' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">页面数量:</text>
            <text class="info-value">{{ vue3Info.pages ? vue3Info.pages.length : 0 }}</text>
          </view>
          <button class="demo-btn warning" @click="checkVue3Package">
            检查Vue3分包状态
          </button>
        </view>
      </view>
      
    </view>
    
    <!-- 使用说明 -->
    <view class="usage-guide">
      <view class="guide-header">
        <text class="guide-title">使用说明</text>
      </view>
      <view class="guide-content">
        <text class="guide-text">1. 点击按钮可以跳转到Vue3分包页面</text>
        <text class="guide-text">2. 可以通过URL参数传递数据到Vue3页面</text>
        <text class="guide-text">3. 使用全局数据在Vue2和Vue3页面间共享状态</text>
        <text class="guide-text">4. 本地存储可以持久化保存数据</text>
        <text class="guide-text">5. 检查分包状态确保功能正常</text>
      </view>
    </view>
  </view>
</template>

<script>
// 注意：这是Vue2语法的示例文件，展示如何在Vue2页面中集成Vue3分包
export default {
  name: 'Vue2IntegrationExample',
  data() {
    return {
      userId: 123,
      message: 'Hello Vue3!',
      globalData: {},
      storedData: '',
      storageInput: '',
      vue3Available: false,
      vue3Info: {}
    }
  },
  onLoad() {
    this.checkVue3Package()
    this.refreshGlobalData()
    this.loadFromStorage()
  },
  methods: {
    // 跳转到Vue3演示页面
    goToVue3Demo() {
      uni.navigateTo({
        url: '/vue3Package/pages/demo/index',
        success: () => {
          console.log('跳转到Vue3演示页面成功')
        },
        fail: (error) => {
          console.error('跳转失败:', error)
          uni.showToast({
            title: '跳转失败',
            icon: 'error'
          })
        }
      })
    },
    
    // 跳转到Vue3测试页面
    goToVue3Test() {
      uni.navigateTo({
        url: '/vue3Package/pages/test/index?from=vue2-integration',
        success: () => {
          console.log('跳转到Vue3测试页面成功')
        },
        fail: (error) => {
          console.error('跳转失败:', error)
          uni.showToast({
            title: '跳转失败',
            icon: 'error'
          })
        }
      })
    },
    
    // 带参数跳转到Vue3页面
    goToVue3DemoWithParams() {
      const params = {
        userId: this.userId,
        message: encodeURIComponent(this.message),
        timestamp: Date.now(),
        from: 'vue2-integration'
      }
      
      const query = Object.keys(params)
        .map(key => `${key}=${params[key]}`)
        .join('&')
      
      uni.navigateTo({
        url: `/vue3Package/pages/demo/index?${query}`,
        success: () => {
          console.log('带参数跳转成功，参数:', params)
        }
      })
    },
    
    // 传递数据到Vue3页面
    passDataToVue3() {
      // 先设置全局数据
      this.setGlobalData()
      
      // 然后跳转
      setTimeout(() => {
        this.goToVue3DemoWithParams()
      }, 100)
    },
    
    // 设置全局数据
    setGlobalData() {
      const app = getApp()
      if (app.globalData) {
        app.globalData.vue2ToVue3Data = {
          userId: this.userId,
          message: this.message,
          timestamp: new Date().toLocaleString(),
          source: 'Vue2 Integration Example'
        }
        this.refreshGlobalData()
        
        uni.showToast({
          title: '全局数据已设置',
          icon: 'success'
        })
      }
    },
    
    // 清空全局数据
    clearGlobalData() {
      const app = getApp()
      if (app.globalData) {
        delete app.globalData.vue2ToVue3Data
        this.refreshGlobalData()
        
        uni.showToast({
          title: '全局数据已清空',
          icon: 'success'
        })
      }
    },
    
    // 刷新全局数据显示
    refreshGlobalData() {
      const app = getApp()
      if (app.globalData) {
        this.globalData = app.globalData.vue2ToVue3Data || {}
      }
    },
    
    // 保存到本地存储
    saveToStorage() {
      if (this.storageInput.trim()) {
        const data = {
          content: this.storageInput,
          timestamp: new Date().toLocaleString(),
          source: 'Vue2 Integration'
        }
        
        uni.setStorageSync('vue2_to_vue3_data', JSON.stringify(data))
        this.loadFromStorage()
        
        uni.showToast({
          title: '数据已保存',
          icon: 'success'
        })
      } else {
        uni.showToast({
          title: '请输入要保存的数据',
          icon: 'none'
        })
      }
    },
    
    // 从本地存储读取
    loadFromStorage() {
      try {
        const stored = uni.getStorageSync('vue2_to_vue3_data')
        if (stored) {
          const data = JSON.parse(stored)
          this.storedData = `${data.content} (${data.timestamp})`
        } else {
          this.storedData = ''
        }
      } catch (error) {
        console.error('读取存储数据失败:', error)
        this.storedData = ''
      }
    },
    
    // 清空本地存储
    clearStorage() {
      uni.removeStorageSync('vue2_to_vue3_data')
      this.loadFromStorage()
      
      uni.showToast({
        title: '存储已清空',
        icon: 'success'
      })
    },
    
    // 检查Vue3分包状态
    checkVue3Package() {
      try {
        // 检查分包页面是否存在
        this.vue3Available = true
        
        // 获取分包信息
        const app = getApp()
        if (app.globalData && app.globalData.vue3PackageInfo) {
          this.vue3Info = app.globalData.vue3PackageInfo
        } else {
          this.vue3Info = {
            version: '1.0.0',
            pages: [
              { name: 'Vue3演示页面' },
              { name: 'Vue3测试页面' }
            ]
          }
        }
        
        console.log('Vue3分包检查完成:', this.vue3Info)
        
      } catch (error) {
        console.error('Vue3分包检查失败:', error)
        this.vue3Available = false
        this.vue3Info = {}
      }
    }
  }
}
</script>

<style scoped>
.vue2-integration-example {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.section {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  padding: 25rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
}

.section-content {
  padding: 30rpx;
}

.demo-btn {
  width: 100%;
  padding: 25rpx;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: white;
  margin-bottom: 20rpx;
}

.demo-btn:last-child {
  margin-bottom: 0;
}

.demo-btn.primary { background: #007aff; }
.demo-btn.secondary { background: #ff9500; }
.demo-btn.success { background: #34c759; }
.demo-btn.info { background: #5ac8fa; }
.demo-btn.warning { background: #ffcc00; color: #333; }

.input-group {
  margin-bottom: 25rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.input-field, .storage-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.data-display, .storage-display {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.data-label, .storage-label {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.data-value, .storage-value {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}

.data-controls, .storage-controls {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.control-btn {
  flex: 1;
  min-width: 150rpx;
  padding: 18rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 20rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.info-value.status-active {
  color: #34c759;
  font-weight: bold;
}

.usage-guide {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.guide-header {
  margin-bottom: 25rpx;
}

.guide-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.guide-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 15rpx;
}

.guide-text:last-child {
  margin-bottom: 0;
}
</style>
