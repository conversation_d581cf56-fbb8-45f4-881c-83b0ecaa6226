# Vue3 测试分包

这是一个在现有Vue2 uniapp项目中集成的Vue3测试分包，用于演示和测试Vue3的Composition API功能。

## 📁 目录结构

```
vue3Package/
├── pages/                    # Vue3页面
│   ├── demo/
│   │   └── index.vue        # Vue3演示页面
│   └── test/
│       └── index.vue        # Vue3测试页面
├── components/              # Vue3组件
│   ├── HelloVue3.vue        # Vue3组件演示
│   └── CompositionDemo.vue  # Composition API演示组件
├── composables/             # 组合式函数
│   ├── useCounter.js        # 计数器组合式函数
│   ├── useApi.js           # API请求组合式函数
│   └── useStorage.js       # 本地存储组合式函数
├── utils/                   # 工具函数
│   └── vue3-utils.js       # Vue3工具函数集合
└── README.md               # 说明文档
```

## 🚀 功能特性

### 1. Vue3 Composition API 演示
- **响应式数据**: 使用 `ref` 和 `reactive` 创建响应式数据
- **计算属性**: 使用 `computed` 创建计算属性
- **监听器**: 使用 `watch` 和 `watchEffect` 监听数据变化
- **生命周期**: 使用新的生命周期钩子函数

### 2. 组合式函数 (Composables)
- **useCounter**: 计数器功能，演示基础的响应式操作
- **useApi**: API请求功能，演示异步数据处理
- **useStorage**: 本地存储功能，演示数据持久化
- **usePagination**: 分页功能，演示复杂的状态管理
- **useForm**: 表单功能，演示表单数据处理

### 3. 实用工具函数
- 防抖和节流函数
- 深拷贝和数组处理
- 日期格式化和数字格式化
- 表单验证工具
- 颜色转换工具

## 📱 页面说明

### 演示页面 (`/vue3Package/pages/demo/index`)
- 展示Vue3基础功能
- 组件通信演示
- 消息历史记录
- 页面导航功能

**访问方式:**
```javascript
uni.navigateTo({
  url: '/vue3Package/pages/demo/index'
})
```

### 测试页面 (`/vue3Package/pages/test/index`)
- 响应式数据测试
- 计算属性测试
- 监听器测试
- 生命周期测试
- 完整的测试套件

**访问方式:**
```javascript
uni.navigateTo({
  url: '/vue3Package/pages/test/index?from=demo'
})
```

## 🔧 使用方法

### 1. 在Vue2页面中跳转到Vue3页面

```javascript
// 跳转到Vue3演示页面
goToVue3Demo() {
  uni.navigateTo({
    url: '/vue3Package/pages/demo/index'
  })
}

// 跳转到Vue3测试页面
goToVue3Test() {
  uni.navigateTo({
    url: '/vue3Package/pages/test/index'
  })
}
```

### 2. 使用组合式函数

```javascript
import { useCounter } from '@/vue3Package/composables/useCounter.js'
import { useApi } from '@/vue3Package/composables/useApi.js'

export default {
  setup() {
    // 使用计数器功能
    const { count, increment, decrement } = useCounter(0)
    
    // 使用API功能
    const { loading, userInfo, fetchUser } = useApi()
    
    return {
      count,
      increment,
      decrement,
      loading,
      userInfo,
      fetchUser
    }
  }
}
```

### 3. 使用工具函数

```javascript
import { 
  debounce, 
  formatDate, 
  validateUtils,
  usePagination 
} from '@/vue3Package/utils/vue3-utils.js'

// 防抖搜索
const debouncedSearch = debounce((keyword) => {
  console.log('搜索:', keyword)
}, 500)

// 格式化日期
const formattedDate = formatDate(new Date(), 'YYYY-MM-DD')

// 验证邮箱
const isValidEmail = validateUtils.isEmail('<EMAIL>')

// 使用分页
const pagination = usePagination({ pageSize: 20 })
```

## 🎯 核心概念演示

### 1. Composition API vs Options API

**Vue2 Options API:**
```javascript
export default {
  data() {
    return {
      count: 0
    }
  },
  methods: {
    increment() {
      this.count++
    }
  }
}
```

**Vue3 Composition API:**
```javascript
import { ref } from 'vue'

export default {
  setup() {
    const count = ref(0)
    
    const increment = () => {
      count.value++
    }
    
    return {
      count,
      increment
    }
  }
}
```

### 2. 响应式系统

```javascript
import { ref, reactive, computed, watch } from 'vue'

export default {
  setup() {
    // 基础类型响应式
    const message = ref('Hello')
    
    // 对象响应式
    const user = reactive({
      name: '张三',
      age: 25
    })
    
    // 计算属性
    const displayName = computed(() => {
      return `用户: ${user.name} (${user.age}岁)`
    })
    
    // 监听器
    watch(message, (newValue, oldValue) => {
      console.log(`消息从 "${oldValue}" 变为 "${newValue}"`)
    })
    
    return {
      message,
      user,
      displayName
    }
  }
}
```

### 3. 生命周期钩子

```javascript
import { 
  onMounted, 
  onUpdated, 
  onBeforeUnmount 
} from 'vue'

export default {
  setup() {
    onMounted(() => {
      console.log('组件已挂载')
    })
    
    onUpdated(() => {
      console.log('组件已更新')
    })
    
    onBeforeUnmount(() => {
      console.log('组件即将卸载')
    })
  }
}
```

## 🔄 数据通信

### 1. 页面间传参

```javascript
// 发送页面
uni.navigateTo({
  url: '/vue3Package/pages/demo/index?userId=123&type=test'
})

// 接收页面
onLoad((options) => {
  console.log('接收参数:', options)
  // { userId: '123', type: 'test' }
})
```

### 2. 全局数据共享

```javascript
// 设置全局数据
getApp().globalData.sharedData = { userId: 123 }

// 获取全局数据
const app = getApp()
const sharedData = app.globalData.sharedData
```

### 3. 本地存储

```javascript
import { useStorage } from '@/vue3Package/composables/useStorage.js'

const { value: userData, save, clear } = useStorage('user_data', {
  name: '',
  email: ''
})

// 数据会自动保存到本地存储
userData.value.name = '张三'
```

## 🧪 测试功能

### 运行测试
1. 访问测试页面: `/vue3Package/pages/test/index`
2. 点击"运行所有测试"按钮
3. 查看测试结果和控制台输出

### 测试项目
- ✅ 响应式数据更新
- ✅ 计算属性自动计算
- ✅ 监听器触发
- ✅ 生命周期钩子执行
- ✅ 组合式函数功能
- ✅ 工具函数正确性

## 📝 开发建议

### 1. 代码组织
- 将相关的响应式逻辑封装到组合式函数中
- 保持组件的setup函数简洁
- 合理使用ref和reactive

### 2. 性能优化
- 使用computed缓存计算结果
- 合理使用watch的immediate和deep选项
- 避免在模板中使用复杂的表达式

### 3. 最佳实践
- 组合式函数以"use"开头命名
- 返回的响应式数据保持一致的命名
- 适当添加类型注释和文档

## 🔗 相关链接

- [Vue3 官方文档](https://v3.cn.vuejs.org/)
- [Composition API 指南](https://v3.cn.vuejs.org/guide/composition-api-introduction.html)
- [uniapp Vue3 支持](https://uniapp.dcloud.net.cn/tutorial/vue3-api.html)

## 📄 更新日志

### v1.0.0 (2025-08-04)
- ✨ 初始版本发布
- ✨ 添加Vue3演示页面
- ✨ 添加Vue3测试页面
- ✨ 添加组合式函数库
- ✨ 添加工具函数集合
- ✨ 完善文档和示例

---

*这个Vue3分包展示了如何在现有的Vue2项目中逐步引入Vue3功能，为项目的渐进式升级提供了实践参考。*
