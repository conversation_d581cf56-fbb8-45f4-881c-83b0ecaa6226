<template>
  <view class="vue3-demo-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">Vue3 演示页面</text>
      <text class="page-subtitle">体验 Composition API 的强大功能</text>
    </view>
    
    <!-- 基础信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">页面信息</text>
      </view>
      <view class="info-item">
        <text class="info-label">Vue版本:</text>
        <text class="info-value">Vue 3.x</text>
      </view>
      <view class="info-item">
        <text class="info-label">页面加载时间:</text>
        <text class="info-value">{{ loadTime }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">访问次数:</text>
        <text class="info-value">{{ visitCount }}</text>
      </view>
    </view>
    
    <!-- Vue3组件演示 -->
    <HelloVue3 
      :message="currentMessage" 
      :title="componentTitle"
      @update="handleMessageUpdate"
      @messageChange="handleMessageChange"
    />
    
    <!-- Composition API功能演示 -->
    <CompositionDemo />
    
    <!-- 页面交互演示 -->
    <view class="interaction-section">
      <view class="section-header">
        <text class="section-title">页面交互演示</text>
      </view>
      
      <view class="interaction-content">
        <view class="input-group">
          <text class="input-label">发送消息:</text>
          <input 
            class="message-input"
            v-model="messageInput"
            placeholder="输入要发送的消息"
            @confirm="sendTestMessage"
          />
          <button class="send-btn" @click="sendTestMessage">发送</button>
        </view>
        
        <view class="message-history" v-if="messageHistory.length > 0">
          <text class="history-title">消息历史:</text>
          <view 
            class="history-item" 
            v-for="(msg, index) in messageHistory" 
            :key="index"
          >
            <text class="history-time">{{ msg.time }}</text>
            <text class="history-content">{{ msg.content }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 导航按钮 -->
    <view class="navigation-section">
      <view class="nav-buttons">
        <button class="nav-btn primary" @click="goToTest">
          前往测试页面
        </button>
        <button class="nav-btn secondary" @click="goBack">
          返回上一页
        </button>
        <button class="nav-btn success" @click="shareCurrentPage">
          分享页面
        </button>
      </view>
    </view>
    
    <!-- 页面底部信息 -->
    <view class="page-footer">
      <text class="footer-text">Vue3 + uniapp 演示项目</text>
      <text class="footer-text">使用 Composition API 构建</text>
    </view>
  </view>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { useCounter } from '../../composables/useCounter.js'
import { useApi } from '../../composables/useApi.js'
import { useStorage } from '../../composables/useStorage.js'
import HelloVue3 from '../../components/HelloVue3.vue'
import CompositionDemo from '../../components/CompositionDemo.vue'

export default {
  name: 'Vue3DemoPage',
  components: {
    HelloVue3,
    CompositionDemo
  },
  setup() {
    // 页面基础数据
    const loadTime = ref('')
    const currentMessage = ref('欢迎使用Vue3!')
    const componentTitle = ref('Vue3组件演示')
    const messageInput = ref('')
    
    // 使用本地存储记录访问次数
    const { value: visitCount } = useStorage('demo_visit_count', 0)
    
    // 消息历史记录
    const messageHistory = reactive([])
    
    // 使用组合式函数
    const { sendMessage } = useApi()
    
    // 计算属性
    const totalMessages = computed(() => messageHistory.length)
    
    // 页面加载处理
    onMounted(() => {
      loadTime.value = new Date().toLocaleString()
      visitCount.value = (visitCount.value || 0) + 1
      
      console.log('Vue3演示页面已加载')
      console.log('访问次数:', visitCount.value)
      
      // 添加欢迎消息
      addMessageToHistory('页面加载完成，欢迎体验Vue3功能!')
    })
    
    // 页面卸载处理
    onUnmounted(() => {
      console.log('Vue3演示页面已卸载')
    })
    
    // 监听消息变化
    watch(currentMessage, (newMessage) => {
      console.log('当前消息更新为:', newMessage)
    })
    
    // 处理消息更新
    const handleMessageUpdate = (newMessage) => {
      currentMessage.value = newMessage
      addMessageToHistory(`组件消息更新: ${newMessage}`)
    }
    
    // 处理消息变化事件
    const handleMessageChange = (changeInfo) => {
      console.log('消息变化详情:', changeInfo)
      addMessageToHistory(`消息从 "${changeInfo.oldMessage}" 变更为 "${changeInfo.newMessage}"`)
    }
    
    // 发送测试消息
    const sendTestMessage = async () => {
      if (!messageInput.value.trim()) {
        uni.showToast({
          title: '请输入消息内容',
          icon: 'none'
        })
        return
      }
      
      try {
        const result = await sendMessage(messageInput.value)
        if (result.success) {
          addMessageToHistory(`发送消息: ${messageInput.value}`)
          messageInput.value = ''
        }
      } catch (error) {
        console.error('发送消息失败:', error)
      }
    }
    
    // 添加消息到历史记录
    const addMessageToHistory = (content) => {
      messageHistory.push({
        content,
        time: new Date().toLocaleTimeString(),
        id: Date.now()
      })
      
      // 限制历史记录数量
      if (messageHistory.length > 10) {
        messageHistory.shift()
      }
    }
    
    // 导航方法
    const goToTest = () => {
      uni.navigateTo({
        url: '/vue3Package/pages/test/index?from=demo'
      })
    }
    
    const goBack = () => {
      uni.navigateBack()
    }
    
    const shareCurrentPage = () => {
      uni.showShareMenu({
        withShareTicket: true,
        success: () => {
          addMessageToHistory('页面分享功能已触发')
        }
      })
    }
    
    return {
      // 基础数据
      loadTime,
      visitCount,
      currentMessage,
      componentTitle,
      messageInput,
      messageHistory,
      totalMessages,
      
      // 事件处理
      handleMessageUpdate,
      handleMessageChange,
      sendTestMessage,
      
      // 导航方法
      goToTest,
      goBack,
      shareCurrentPage
    }
  }
}
</script>

<style scoped>
.vue3-demo-page {
  min-height: 100vh;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
  padding: 20rpx;
}

.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 42rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.info-card {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  margin-bottom: 25rpx;
  text-align: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.interaction-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.message-input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.send-btn {
  width: 100%;
  padding: 20rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.message-history {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.history-title {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.history-item {
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.history-time {
  font-size: 22rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}

.history-content {
  font-size: 26rpx;
  color: #333;
}

.navigation-section {
  margin-bottom: 30rpx;
}

.nav-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.nav-btn {
  padding: 25rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: white;
}

.nav-btn.primary {
  background: #007aff;
}

.nav-btn.secondary {
  background: #ff9500;
}

.nav-btn.success {
  background: #34c759;
}

.page-footer {
  text-align: center;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
}

.footer-text {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.footer-text:last-child {
  margin-bottom: 0;
}
</style>
