/**
 * Vue3 分包入口文件
 * 提供统一的导出和工具方法
 */

// 导出所有组合式函数
export { useCounter } from './composables/useCounter.js'
export { useApi } from './composables/useApi.js'
export { useStorage, useUserPreferences } from './composables/useStorage.js'

// 导出工具函数
export {
  debounce,
  throttle,
  deepClone,
  formatDate,
  generateId,
  uniqueArray,
  sortArray,
  formatNumber,
  formatFileSize,
  parseUrlParams,
  colorUtils,
  validateUtils,
  usePagination,
  useForm
} from './utils/vue3-utils.js'

/**
 * Vue3分包导航工具
 */
export const vue3Navigation = {
  /**
   * 跳转到Vue3演示页面
   * @param {Object} params 页面参数
   */
  goToDemo(params = {}) {
    const query = Object.keys(params).length > 0 
      ? '?' + Object.keys(params).map(key => `${key}=${params[key]}`).join('&')
      : ''
    
    uni.navigateTo({
      url: `/vue3Package/pages/demo/index${query}`,
      success: () => {
        console.log('跳转到Vue3演示页面成功')
      },
      fail: (error) => {
        console.error('跳转到Vue3演示页面失败:', error)
      }
    })
  },

  /**
   * 跳转到Vue3测试页面
   * @param {Object} params 页面参数
   */
  goToTest(params = {}) {
    const query = Object.keys(params).length > 0 
      ? '?' + Object.keys(params).map(key => `${key}=${params[key]}`).join('&')
      : ''
    
    uni.navigateTo({
      url: `/vue3Package/pages/test/index${query}`,
      success: () => {
        console.log('跳转到Vue3测试页面成功')
      },
      fail: (error) => {
        console.error('跳转到Vue3测试页面失败:', error)
      }
    })
  },

  /**
   * 检查Vue3分包是否可用
   * @returns {boolean} 是否可用
   */
  isAvailable() {
    try {
      // 检查Vue3相关API是否存在
      return typeof uni !== 'undefined' && 
             typeof getCurrentPages === 'function'
    } catch (error) {
      console.error('Vue3分包不可用:', error)
      return false
    }
  }
}

/**
 * Vue3分包信息
 */
export const vue3PackageInfo = {
  name: 'Vue3 Test Package',
  version: '1.0.0',
  description: 'Vue3功能演示和测试分包',
  author: 'Augment Agent',
  pages: [
    {
      path: '/vue3Package/pages/demo/index',
      name: 'Vue3演示页面',
      description: '展示Vue3基础功能和组件通信'
    },
    {
      path: '/vue3Package/pages/test/index',
      name: 'Vue3测试页面',
      description: '完整的Vue3功能测试套件'
    }
  ],
  components: [
    {
      name: 'HelloVue3',
      path: '/vue3Package/components/HelloVue3.vue',
      description: 'Vue3组件演示'
    },
    {
      name: 'CompositionDemo',
      path: '/vue3Package/components/CompositionDemo.vue',
      description: 'Composition API功能演示'
    }
  ],
  composables: [
    {
      name: 'useCounter',
      description: '计数器组合式函数'
    },
    {
      name: 'useApi',
      description: 'API请求组合式函数'
    },
    {
      name: 'useStorage',
      description: '本地存储组合式函数'
    }
  ]
}

/**
 * 初始化Vue3分包
 * 在应用启动时调用
 */
export function initVue3Package() {
  console.log('Vue3分包初始化开始')
  console.log('分包信息:', vue3PackageInfo)
  
  // 检查环境
  if (!vue3Navigation.isAvailable()) {
    console.warn('Vue3分包环境检查失败')
    return false
  }
  
  // 设置全局Vue3分包标识
  if (typeof getApp === 'function') {
    const app = getApp()
    if (app.globalData) {
      app.globalData.vue3PackageEnabled = true
      app.globalData.vue3PackageInfo = vue3PackageInfo
    }
  }
  
  console.log('Vue3分包初始化完成')
  return true
}

// 默认导出
export default {
  navigation: vue3Navigation,
  info: vue3PackageInfo,
  init: initVue3Package
}
