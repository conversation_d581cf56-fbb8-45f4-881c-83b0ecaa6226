import { ref, reactive } from 'vue'

/**
 * API请求组合式函数
 * 演示Vue3中的异步数据处理
 */
export function useApi() {
  // 加载状态
  const loading = ref(false)
  const error = ref(null)
  
  // 用户信息（响应式对象）
  const userInfo = reactive({
    name: '',
    email: '',
    avatar: '',
    id: null
  })
  
  // 模拟获取用户信息
  const fetchUser = async (userId = 123) => {
    loading.value = true
    error.value = null
    
    try {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟API响应数据
      const mockUserData = {
        id: userId,
        name: '张三',
        email: '<EMAIL>',
        avatar: 'https://via.placeholder.com/100x100'
      }
      
      // 更新用户信息
      Object.assign(userInfo, mockUserData)
      
      console.log('用户信息获取成功:', userInfo)
      
    } catch (err) {
      error.value = '获取用户信息失败: ' + err.message
      console.error('API请求失败:', err)
    } finally {
      loading.value = false
    }
  }
  
  // 模拟发送消息
  const sendMessage = async (message) => {
    loading.value = true
    error.value = null
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      uni.showToast({
        title: '消息发送成功',
        icon: 'success'
      })
      
      return { success: true, message: '发送成功' }
      
    } catch (err) {
      error.value = '发送失败: ' + err.message
      uni.showToast({
        title: '发送失败',
        icon: 'error'
      })
      return { success: false, error: err.message }
    } finally {
      loading.value = false
    }
  }
  
  // 清空用户信息
  const clearUser = () => {
    Object.assign(userInfo, {
      name: '',
      email: '',
      avatar: '',
      id: null
    })
  }
  
  return {
    loading,
    error,
    userInfo,
    fetchUser,
    sendMessage,
    clearUser
  }
}
