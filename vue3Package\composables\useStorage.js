import { ref, watch } from 'vue'

/**
 * 本地存储组合式函数
 * 演示Vue3中的数据持久化
 */
export function useStorage(key, defaultValue = null) {
  // 从本地存储读取初始值
  const storedValue = uni.getStorageSync(key)
  const initialValue = storedValue !== '' ? JSON.parse(storedValue) : defaultValue
  
  // 创建响应式引用
  const value = ref(initialValue)
  
  // 监听值的变化，自动保存到本地存储
  watch(
    value,
    (newValue) => {
      if (newValue === null || newValue === undefined) {
        uni.removeStorageSync(key)
      } else {
        uni.setStorageSync(key, JSON.stringify(newValue))
      }
    },
    { deep: true }
  )
  
  // 手动保存
  const save = () => {
    if (value.value === null || value.value === undefined) {
      uni.removeStorageSync(key)
    } else {
      uni.setStorageSync(key, JSON.stringify(value.value))
    }
  }
  
  // 清除存储
  const clear = () => {
    value.value = defaultValue
    uni.removeStorageSync(key)
  }
  
  // 重新加载
  const reload = () => {
    const storedValue = uni.getStorageSync(key)
    value.value = storedValue !== '' ? JSON.parse(storedValue) : defaultValue
  }
  
  return {
    value,
    save,
    clear,
    reload
  }
}

/**
 * 用户偏好设置组合式函数
 */
export function useUserPreferences() {
  const { value: preferences, save, clear } = useStorage('user_preferences', {
    theme: 'light',
    language: 'zh-CN',
    notifications: true,
    autoSave: true
  })
  
  // 设置主题
  const setTheme = (theme) => {
    preferences.value.theme = theme
  }
  
  // 设置语言
  const setLanguage = (language) => {
    preferences.value.language = language
  }
  
  // 切换通知
  const toggleNotifications = () => {
    preferences.value.notifications = !preferences.value.notifications
  }
  
  // 切换自动保存
  const toggleAutoSave = () => {
    preferences.value.autoSave = !preferences.value.autoSave
  }
  
  return {
    preferences,
    setTheme,
    setLanguage,
    toggleNotifications,
    toggleAutoSave,
    save,
    clear
  }
}
