/**
 * Vue3 工具函数集合
 * 提供常用的工具方法和辅助函数
 */

import { ref, reactive, computed, watch, nextTick } from 'vue'

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间(毫秒)
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 节流间隔(毫秒)
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300) {
  let lastTime = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastTime >= delay) {
      lastTime = now
      return func.apply(this, args)
    }
  }
}

/**
 * 深拷贝函数
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 格式化日期
 * @param {Date|string|number} date 日期
 * @param {string} format 格式字符串
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  const d = new Date(date)
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 生成唯一ID
 * @param {string} prefix 前缀
 * @returns {string} 唯一ID
 */
export function generateId(prefix = 'id') {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 数组去重
 * @param {Array} arr 原数组
 * @param {string} key 对象数组的去重键名
 * @returns {Array} 去重后的数组
 */
export function uniqueArray(arr, key = null) {
  if (!Array.isArray(arr)) return []
  
  if (key) {
    // 对象数组去重
    const seen = new Set()
    return arr.filter(item => {
      const value = item[key]
      if (seen.has(value)) {
        return false
      }
      seen.add(value)
      return true
    })
  } else {
    // 基础类型数组去重
    return [...new Set(arr)]
  }
}

/**
 * 对象数组排序
 * @param {Array} arr 要排序的数组
 * @param {string} key 排序键名
 * @param {string} order 排序方向 'asc' | 'desc'
 * @returns {Array} 排序后的数组
 */
export function sortArray(arr, key, order = 'asc') {
  if (!Array.isArray(arr)) return []
  
  return arr.sort((a, b) => {
    const valueA = a[key]
    const valueB = b[key]
    
    if (order === 'desc') {
      return valueB > valueA ? 1 : valueB < valueA ? -1 : 0
    } else {
      return valueA > valueB ? 1 : valueA < valueB ? -1 : 0
    }
  })
}

/**
 * 数字格式化
 * @param {number} num 数字
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num, decimals = 2) {
  if (isNaN(num)) return '0'
  
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

/**
 * 文件大小格式化
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * URL参数解析
 * @param {string} url URL字符串
 * @returns {Object} 参数对象
 */
export function parseUrlParams(url = window.location.href) {
  const params = {}
  const urlObj = new URL(url)
  
  for (const [key, value] of urlObj.searchParams) {
    params[key] = value
  }
  
  return params
}

/**
 * 颜色转换工具
 */
export const colorUtils = {
  /**
   * HEX转RGB
   * @param {string} hex HEX颜色值
   * @returns {Object} RGB对象
   */
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  },
  
  /**
   * RGB转HEX
   * @param {number} r 红色值
   * @param {number} g 绿色值
   * @param {number} b 蓝色值
   * @returns {string} HEX颜色值
   */
  rgbToHex(r, g, b) {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
  }
}

/**
 * 表单验证工具
 */
export const validateUtils = {
  /**
   * 验证邮箱
   * @param {string} email 邮箱地址
   * @returns {boolean} 是否有效
   */
  isEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },
  
  /**
   * 验证手机号
   * @param {string} phone 手机号
   * @returns {boolean} 是否有效
   */
  isPhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },
  
  /**
   * 验证身份证号
   * @param {string} idCard 身份证号
   * @returns {boolean} 是否有效
   */
  isIdCard(idCard) {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return idCardRegex.test(idCard)
  }
}

/**
 * 创建响应式的分页数据
 * @param {Object} options 配置选项
 * @returns {Object} 分页相关的响应式数据和方法
 */
export function usePagination(options = {}) {
  const {
    pageSize = 10,
    initialPage = 1
  } = options
  
  const currentPage = ref(initialPage)
  const total = ref(0)
  const loading = ref(false)
  const data = ref([])
  
  const totalPages = computed(() => {
    return Math.ceil(total.value / pageSize)
  })
  
  const hasNextPage = computed(() => {
    return currentPage.value < totalPages.value
  })
  
  const hasPrevPage = computed(() => {
    return currentPage.value > 1
  })
  
  const nextPage = () => {
    if (hasNextPage.value) {
      currentPage.value++
    }
  }
  
  const prevPage = () => {
    if (hasPrevPage.value) {
      currentPage.value--
    }
  }
  
  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }
  
  const reset = () => {
    currentPage.value = initialPage
    total.value = 0
    data.value = []
  }
  
  return {
    currentPage,
    total,
    loading,
    data,
    totalPages,
    hasNextPage,
    hasPrevPage,
    nextPage,
    prevPage,
    goToPage,
    reset
  }
}

/**
 * 创建响应式的表单数据
 * @param {Object} initialData 初始数据
 * @returns {Object} 表单相关的响应式数据和方法
 */
export function useForm(initialData = {}) {
  const formData = reactive({ ...initialData })
  const errors = reactive({})
  const loading = ref(false)
  
  const setField = (key, value) => {
    formData[key] = value
    // 清除该字段的错误
    if (errors[key]) {
      delete errors[key]
    }
  }
  
  const setError = (key, message) => {
    errors[key] = message
  }
  
  const clearErrors = () => {
    Object.keys(errors).forEach(key => {
      delete errors[key]
    })
  }
  
  const reset = () => {
    Object.assign(formData, initialData)
    clearErrors()
  }
  
  const hasErrors = computed(() => {
    return Object.keys(errors).length > 0
  })
  
  return {
    formData,
    errors,
    loading,
    setField,
    setError,
    clearErrors,
    reset,
    hasErrors
  }
}
